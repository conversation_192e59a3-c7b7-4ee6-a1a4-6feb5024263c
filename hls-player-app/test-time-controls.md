# Historical Time Control Testing Guide

## Test Scenarios

### 1. Basic Time Control
- [ ] Open the application in browser (http://localhost:3001)
- [ ] Click on a historical transmission in the timeline
- [ ] Verify the player switches to historical mode
- [ ] Verify the time display shows the correct historical time
- [ ] Verify the player starts in paused state (time should not advance)

### 2. Play/Pause Functionality
- [ ] Click the Play button
- [ ] Verify the time starts advancing (should increment every second)
- [ ] Click the Pause button
- [ ] Verify the time stops advancing immediately
- [ ] Verify audio playback stops when paused

### 3. Jump Controls
- [ ] Click the "+15s" button
- [ ] Verify the time jumps forward by 15 seconds
- [ ] Click the "-15s" button
- [ ] Verify the time jumps backward by 15 seconds
- [ ] Test multiple rapid clicks to ensure responsiveness

### 4. Audio Synchronization
- [ ] Start playback in historical mode
- [ ] Verify audio plays when there's a recording for the current time
- [ ] Jump to a time with no recording
- [ ] Verify audio stops appropriately
- [ ] Jump back to a time with recording
- [ ] Verify audio resumes correctly

### 5. Mode Switching
- [ ] Switch from historical to live mode
- [ ] Verify time controls are disabled in live mode
- [ ] Switch back to historical mode
- [ ] Verify time controls are re-enabled

## Expected Behavior

### Time Control System
- Historical time should be the primary control
- Play/pause should immediately affect time progression
- Jump buttons should provide instant feedback
- Time should only advance when playing

### Audio Playback
- Audio should follow the historical time
- Audio should start/stop based on available recordings
- Audio should sync to the current historical time position

### UI Responsiveness
- All controls should respond immediately
- Time display should update in real-time during playback
- Button states should reflect current playback state

## Success Criteria
✅ Time stops advancing when paused
✅ Jump controls work immediately
✅ Audio follows historical time correctly
✅ No time drift or synchronization issues
✅ Clean state transitions between modes
