# HLS Player App - Complete System Architecture

## Overview

The HLS Player App is a React-based application that provides both live HLS streaming and historical audio playback capabilities. The system is designed with a clean separation between time control logic and audio playback, ensuring predictable behavior and maintainable code.

## Core Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        UnifiedPlayer                            │
│  - Main container component                                     │
│  - Handles mode switching (live/historical)                    │
│  - Manages timeline interactions                               │
└─────────────────┬───────────────────────────────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────────────────────────────────┐
│                    usePlaybackMode Hook                        │
│  - Manages playback mode state (live/historical)               │
│  - Integrates with historical time control system              │
│  - Handles mode transitions                                    │
└─────────────────┬───────────────────────────────────────────────┘
                  │
        ┌─────────┴─────────┐
        ▼                   ▼
┌─────────────────┐ ┌─────────────────────────────────────┐
│   HLSPlayer     │ │    TimeBasedHistoricalPlayer        │
│  - Live streams │ │  - Historical audio playback       │
│  - Always plays │ │  - Time-controlled audio           │
│  - Volume only  │ │  - Segment progression             │
└─────────────────┘ └─────────────────┬───────────────────┘
                                      │
                                      ▼
                    ┌─────────────────────────────────────┐
                    │    useHistoricalTimeControl Hook   │
                    │  - Primary time control system     │
                    │  - Play/pause/seek functionality   │
                    │  - Jump controls (+15s/-15s)       │
                    └─────────────────────────────────────┘
```

## Key Components and Hooks

### 1. UnifiedPlayer Component
**Location**: `src/components/unified-player.tsx`

**Purpose**: Main container that orchestrates the entire playback system.

**Key Responsibilities**:
- Renders either HLSPlayer or TimeBasedHistoricalPlayer based on mode
- <PERSON><PERSON> timeline transmission selection
- Manages keyboard shortcuts (Esc to return to live)
- Provides user feedback via toasts

**Key State Variables**:
- `playbackState`: Current mode and transition state
- `currentTime`: Historical time (null in live mode)
- `isLive`/`isHistorical`: Mode indicators
- `historicalTimeControls`: Time control functions for historical mode

### 2. usePlaybackMode Hook
**Location**: `src/hooks/use-playback-mode.ts`

**Purpose**: Central state management for switching between live and historical modes.

**Key State Variables**:
```typescript
interface PlaybackState {
  mode: 'live' | 'historical'
  currentRecording?: HistoricalRecording
  selectedTransmission?: TimelineTransmission
  isTransitioning: boolean
}
```

**Key Functions**:
- `switchToLive()`: Resets historical controls and switches to live mode
- `switchToHistorical(transmission)`: Sets initial time and switches to historical mode
- `clearSelection()`: Clears current selection without changing mode

**Integration**: Uses `useHistoricalTimeControl` hook internally for time management.

### 3. useHistoricalTimeControl Hook
**Location**: `src/hooks/use-historical-time-control.ts`

**Purpose**: Primary time control system for historical playback.

**Key State Variables**:
```typescript
interface HistoricalTimeState {
  currentTime: Date | null      // Current historical time
  isPlaying: boolean           // Whether time is advancing
  playbackRate: number         // Speed multiplier (currently 1.0)
}
```

**Key Functions**:
- `play()`: Starts time progression
- `pause()`: Stops time progression
- `togglePlayPause()`: Toggles between play/pause
- `seekTo(time)`: Jump to specific time
- `jumpForward(seconds)`: Jump forward by specified seconds
- `jumpBackward(seconds)`: Jump backward by specified seconds
- `setInitialTime(time)`: Set starting time (used when entering historical mode)
- `reset()`: Reset to initial state

**Time Progression Logic**:
- Uses `setInterval` to increment time by 1 second every 1000ms
- Only runs when `isPlaying` is true
- Automatically stops when component unmounts

### 4. HLSPlayer Component
**Location**: `src/components/hls-player.tsx`

**Purpose**: Handles live HLS streaming with automatic playback.

**Key Features**:
- **Always Auto-Play**: Live streams start automatically (no play/pause button)
- **Volume Control**: Users can mute/unmute and adjust volume
- **Network Adaptation**: Automatically adjusts quality based on network conditions
- **Retry Logic**: Attempts to start playback on user interaction if autoplay fails

**Key State Variables**:
- `isPlaying`: Whether audio is currently playing
- `isMuted`: Mute state
- `volume`: Volume level (0-1)
- `isLoading`: Loading state
- `streamStatus`: Stream availability status

**Browser Autoplay Handling**:
- Attempts autoplay immediately
- Falls back to "Click to start" if blocked by browser
- Retries on any user interaction

### 5. TimeBasedHistoricalPlayer Component
**Location**: `src/components/time-based-historical-player.tsx`

**Purpose**: Manages historical audio playback based on controlled time progression.

**Key Features**:
- **Time-Controlled Audio**: Audio playback follows historical time
- **Automatic Segment Switching**: Changes recordings as time progresses
- **Precise Synchronization**: Keeps audio in sync with historical time
- **User Controls**: Play/pause/jump controls that affect time progression

**Key State Variables**:
- `currentRecording`: Currently loaded audio recording
- `isMuted`: Audio mute state
- `volume`: Audio volume level
- `error`: Playback error messages

**Audio Management Logic**:
1. **Recording Selection**: `findCurrentRecording(time)` finds the appropriate recording for current time
2. **Audio Loading**: Loads new audio source when switching recordings
3. **Position Sync**: Sets audio position based on time within the recording
4. **Play/Pause Control**: Audio only plays when `isPlaying` prop is true
5. **Drift Correction**: Automatically corrects audio position if drift > 2 seconds

## Data Flow

### Live Mode Flow
```
User opens app → HLSPlayer loads → Auto-play starts → Audio streams continuously
                                ↓
                         User can only mute/unmute
```

### Historical Mode Flow
```
User clicks timeline → switchToHistorical() → setInitialTime() → TimeBasedHistoricalPlayer
                                                                         ↓
User controls time → Historical time updates → Audio follows time → Segments switch automatically
```

### Time Control Flow (Historical Mode)
```
User clicks play → timeControls.play() → Time starts advancing → Audio plays current segment
                                                                         ↓
Time reaches new segment → findCurrentRecording() → Switch audio → Continue playback
                                                                         ↓
User clicks pause → timeControls.pause() → Time stops → Audio pauses
```

## State Synchronization

### Historical Mode State Coordination
1. **Time Control State**: Managed by `useHistoricalTimeControl`
2. **Audio State**: Managed by `TimeBasedHistoricalPlayer`
3. **Synchronization**: Audio state follows time control state via props and effects

### Key Synchronization Points
- `isPlaying` prop controls whether audio plays or pauses
- `currentTime` prop determines which recording to play and at what position
- Time control changes immediately affect audio playback
- Audio loading/switching is transparent to time control system

## Error Handling

### Live Mode Errors
- Autoplay blocked: Shows "Click to start" message
- Network issues: Shows connection status and retry option
- Stream unavailable: Shows error message with refresh option

### Historical Mode Errors
- Audio loading failures: Shows error message, continues time progression
- Missing recordings: Silently skips gaps in timeline
- Sync issues: Automatically corrects drift, logs warnings

## Performance Considerations

### Time Control Optimization
- Single interval timer for time progression
- Efficient state updates using React's batching
- Cleanup of intervals on component unmount

### Audio Optimization
- Lazy loading of audio files
- Reuse of audio elements when possible
- Drift correction only when necessary (>2s difference)

### Network Optimization
- Adaptive HLS quality based on network conditions
- Efficient API calls for timeline data
- Caching of timeline information

## Browser Compatibility

### Autoplay Policies
- **Chrome/Edge**: Requires user interaction for autoplay with sound
- **Safari**: More permissive, often allows autoplay
- **Firefox**: Similar to Chrome, blocks autoplay by default

### HLS Support
- **Native Support**: Safari (iOS/macOS)
- **HLS.js**: All other modern browsers
- **Fallback**: Error message for unsupported browsers

## Configuration

### Key Configuration Points
- `config.player.autoPlay`: Controls initial autoplay behavior
- `config.player.refreshIntervals`: Status check intervals
- HLS quality settings: Adaptive based on network detection
- Time progression rate: Currently fixed at 1:1 (real-time)

This architecture provides a clean, maintainable system with clear separation of concerns and predictable behavior for both live and historical playback modes.
