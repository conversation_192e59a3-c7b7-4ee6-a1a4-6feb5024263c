"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_test-api_ts";
exports.ids = ["_ssr_src_lib_test-api_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/test-api.ts":
/*!*****************************!*\
  !*** ./src/lib/test-api.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   testHistoricalAPI: () => (/* binding */ testHistoricalAPI)\n/* harmony export */ });\n/* harmony import */ var _historical_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./historical-api */ \"(ssr)/./src/lib/historical-api.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(ssr)/./src/lib/config.ts\");\n/**\n * Simple API test utility for development\n */ \n\nasync function testHistoricalAPI() {\n    console.log('🧪 Testing Historical API...');\n    try {\n        const stationId = _config__WEBPACK_IMPORTED_MODULE_1__.config.player.defaultStationId;\n        const fullStationId = (0,_config__WEBPACK_IMPORTED_MODULE_1__.getRecordingsStationId)();\n        const testDate = new Date();\n        testDate.setDate(testDate.getDate() - 1); // Yesterday\n        console.log(`📡 Testing with station: ${stationId} (${fullStationId})`);\n        console.log(`📅 Testing date: ${testDate.toISOString().slice(0, 10)}`);\n        // Test single day fetch\n        const dailyRecordings = await _historical_api__WEBPACK_IMPORTED_MODULE_0__.historicalAPI.fetchDailyRecordings(stationId, testDate);\n        if (dailyRecordings) {\n            console.log('✅ API Response received:');\n            console.log(`   - Station: ${dailyRecordings.station}`);\n            console.log(`   - Date: ${dailyRecordings.date}`);\n            console.log(`   - Total recordings: ${dailyRecordings.total_recordings}`);\n            console.log(`   - Total duration: ${Math.floor(dailyRecordings.total_duration_ms / 1000)}s`);\n            if (dailyRecordings.recordings.length > 0) {\n                const firstRecording = dailyRecordings.recordings[0];\n                console.log('   - First recording:');\n                console.log(`     * Time: ${firstRecording.start_time}`);\n                console.log(`     * Duration: ${Math.floor(firstRecording.duration_ms / 1000)}s`);\n                console.log(`     * URL: ${firstRecording.download_url}`);\n            }\n        } else {\n            console.log('⚠️  No recordings found for this date');\n        }\n        // Test cache\n        console.log('\\n🗄️  Testing cache...');\n        const cacheStats = _historical_api__WEBPACK_IMPORTED_MODULE_0__.historicalAPI.getCacheStats();\n        console.log(`   - Cache size: ${cacheStats.size}`);\n        console.log(`   - Cache keys: ${cacheStats.keys.join(', ')}`);\n        return dailyRecordings;\n    } catch (error) {\n        console.error('❌ API Test failed:', error);\n        return null;\n    }\n}\n// Auto-run test in development\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/test-api.ts\n");

/***/ })

};
;