"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_lib_test-api_ts"],{

/***/ "(app-pages-browser)/./src/lib/test-api.ts":
/*!*****************************!*\
  !*** ./src/lib/test-api.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   testHistoricalAPI: () => (/* binding */ testHistoricalAPI)\n/* harmony export */ });\n/* harmony import */ var _historical_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./historical-api */ \"(app-pages-browser)/./src/lib/historical-api.ts\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(app-pages-browser)/./src/lib/config.ts\");\n/**\n * Simple API test utility for development\n */ \n\nasync function testHistoricalAPI() {\n    console.log('🧪 Testing Historical API...');\n    try {\n        const stationId = _config__WEBPACK_IMPORTED_MODULE_1__.config.player.defaultStationId;\n        const fullStationId = (0,_config__WEBPACK_IMPORTED_MODULE_1__.getRecordingsStationId)();\n        const testDate = new Date();\n        testDate.setDate(testDate.getDate() - 1); // Yesterday\n        console.log(\"\\uD83D\\uDCE1 Testing with station: \".concat(stationId, \" (\").concat(fullStationId, \")\"));\n        console.log(\"\\uD83D\\uDCC5 Testing date: \".concat(testDate.toISOString().slice(0, 10)));\n        // Test single day fetch\n        const dailyRecordings = await _historical_api__WEBPACK_IMPORTED_MODULE_0__.historicalAPI.fetchDailyRecordings(stationId, testDate);\n        if (dailyRecordings) {\n            console.log('✅ API Response received:');\n            console.log(\"   - Station: \".concat(dailyRecordings.station));\n            console.log(\"   - Date: \".concat(dailyRecordings.date));\n            console.log(\"   - Total recordings: \".concat(dailyRecordings.total_recordings));\n            console.log(\"   - Total duration: \".concat(Math.floor(dailyRecordings.total_duration_ms / 1000), \"s\"));\n            if (dailyRecordings.recordings.length > 0) {\n                const firstRecording = dailyRecordings.recordings[0];\n                console.log('   - First recording:');\n                console.log(\"     * Time: \".concat(firstRecording.start_time));\n                console.log(\"     * Duration: \".concat(Math.floor(firstRecording.duration_ms / 1000), \"s\"));\n                console.log(\"     * URL: \".concat(firstRecording.download_url));\n            }\n        } else {\n            console.log('⚠️  No recordings found for this date');\n        }\n        // Test cache\n        console.log('\\n🗄️  Testing cache...');\n        const cacheStats = _historical_api__WEBPACK_IMPORTED_MODULE_0__.historicalAPI.getCacheStats();\n        console.log(\"   - Cache size: \".concat(cacheStats.size));\n        console.log(\"   - Cache keys: \".concat(cacheStats.keys.join(', ')));\n        return dailyRecordings;\n    } catch (error) {\n        console.error('❌ API Test failed:', error);\n        return null;\n    }\n}\n// Auto-run test in development\nif (true) {\n    // Run test after a short delay to avoid blocking initial render\n    setTimeout(()=>{\n        testHistoricalAPI();\n    }, 2000);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/test-api.ts\n"));

/***/ })

}]);