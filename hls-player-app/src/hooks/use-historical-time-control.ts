/**
 * Historical Time Control Hook
 * 
 * Manages historical time progression with user controls (play/pause/seek/jump)
 * This is the primary time control system for historical playback mode
 */

import { useState, useCallback, useRef, useEffect } from 'react'

export interface HistoricalTimeState {
  currentTime: Date | null
  isPlaying: boolean
  playbackRate: number
}

export interface HistoricalTimeControls {
  // Playback controls
  play: () => void
  pause: () => void
  togglePlayPause: () => void
  
  // Time navigation
  seekTo: (time: Date) => void
  jumpForward: (seconds: number) => void
  jumpBackward: (seconds: number) => void
  
  // Initialization
  setInitialTime: (time: Date) => void
  reset: () => void
}

export interface UseHistoricalTimeControlReturn {
  // State
  timeState: HistoricalTimeState
  
  // Controls
  controls: HistoricalTimeControls
  
  // Computed properties
  currentTime: Date | null
  isPlaying: boolean
}

export const useHistoricalTimeControl = (): UseHistoricalTimeControlReturn => {
  const [timeState, setTimeState] = useState<HistoricalTimeState>({
    currentTime: null,
    isPlaying: false,
    playbackRate: 1.0
  })

  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  /**
   * Start time progression
   */
  const startTimeProgression = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }

    intervalRef.current = setInterval(() => {
      setTimeState(prev => {
        if (!prev.currentTime || !prev.isPlaying) {
          return prev
        }

        return {
          ...prev,
          currentTime: new Date(prev.currentTime.getTime() + (1000 * prev.playbackRate))
        }
      })
    }, 1000)
  }, [])

  /**
   * Stop time progression
   */
  const stopTimeProgression = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
  }, [])

  /**
   * Play - start time progression
   */
  const play = useCallback(() => {
    setTimeState(prev => ({ ...prev, isPlaying: true }))
    startTimeProgression()
  }, [startTimeProgression])

  /**
   * Pause - stop time progression
   */
  const pause = useCallback(() => {
    setTimeState(prev => ({ ...prev, isPlaying: false }))
    stopTimeProgression()
  }, [stopTimeProgression])

  /**
   * Toggle play/pause
   */
  const togglePlayPause = useCallback(() => {
    setTimeState(prev => {
      const newIsPlaying = !prev.isPlaying
      
      if (newIsPlaying) {
        startTimeProgression()
      } else {
        stopTimeProgression()
      }
      
      return { ...prev, isPlaying: newIsPlaying }
    })
  }, [startTimeProgression, stopTimeProgression])

  /**
   * Seek to specific time
   */
  const seekTo = useCallback((time: Date) => {
    setTimeState(prev => ({
      ...prev,
      currentTime: new Date(time)
    }))
  }, [])

  /**
   * Jump forward by specified seconds
   */
  const jumpForward = useCallback((seconds: number) => {
    setTimeState(prev => {
      if (!prev.currentTime) return prev
      
      return {
        ...prev,
        currentTime: new Date(prev.currentTime.getTime() + (seconds * 1000))
      }
    })
  }, [])

  /**
   * Jump backward by specified seconds
   */
  const jumpBackward = useCallback((seconds: number) => {
    setTimeState(prev => {
      if (!prev.currentTime) return prev
      
      return {
        ...prev,
        currentTime: new Date(prev.currentTime.getTime() - (seconds * 1000))
      }
    })
  }, [])

  /**
   * Set initial time (when entering historical mode)
   */
  const setInitialTime = useCallback((time: Date) => {
    setTimeState(prev => ({
      ...prev,
      currentTime: new Date(time),
      isPlaying: false // Start paused by default
    }))
  }, [])

  /**
   * Reset to initial state
   */
  const reset = useCallback(() => {
    stopTimeProgression()
    setTimeState({
      currentTime: null,
      isPlaying: false,
      playbackRate: 1.0
    })
  }, [stopTimeProgression])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  // Controls object
  const controls: HistoricalTimeControls = {
    play,
    pause,
    togglePlayPause,
    seekTo,
    jumpForward,
    jumpBackward,
    setInitialTime,
    reset
  }

  return {
    timeState,
    controls,
    currentTime: timeState.currentTime,
    isPlaying: timeState.isPlaying
  }
}
