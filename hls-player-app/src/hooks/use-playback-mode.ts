/**
 * Playback Mode Management Hook
 *
 * Manages switching between live HLS streaming and historical audio playback
 * Integrates with the new historical time control system
 */

import { useState, useCallback, useRef } from 'react'
import type { PlaybackMode, PlaybackState, TimelineTransmission, HistoricalRecording } from '@/types'
import { useHistoricalTimeControl } from './use-historical-time-control'

export interface UsePlaybackModeReturn {
  // State
  playbackState: PlaybackState
  currentTime: Date | null

  // Actions
  switchToLive: () => void
  switchToHistorical: (transmission: TimelineTransmission) => void
  clearSelection: () => void

  // Status
  isLive: boolean
  isHistorical: boolean
  isTransitioning: boolean

  // Historical time controls (only available in historical mode)
  historicalTimeControls: ReturnType<typeof useHistoricalTimeControl>['controls'] | null
  isHistoricalPlaying: boolean
}

export const usePlaybackMode = (): UsePlaybackModeReturn => {
  const [playbackState, setPlaybackState] = useState<PlaybackState>({
    mode: 'live',
    isTransitioning: false
  })

  const transitionTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Historical time control system
  const historicalTimeControl = useHistoricalTimeControl()

  /**
   * Set transitioning state with automatic cleanup
   */
  const setTransitioning = useCallback((isTransitioning: boolean, duration: number = 500) => {
    if (transitionTimeoutRef.current) {
      clearTimeout(transitionTimeoutRef.current)
    }

    setPlaybackState(prev => ({
      ...prev,
      isTransitioning
    }))

    if (isTransitioning) {
      transitionTimeoutRef.current = setTimeout(() => {
        setPlaybackState(prev => ({
          ...prev,
          isTransitioning: false
        }))
      }, duration)
    }
  }, [])

  /**
   * Switch to live streaming mode
   */
  const switchToLive = useCallback(() => {
    console.log('Switching to live mode')

    // Reset historical time control
    historicalTimeControl.controls.reset()
    setTransitioning(true)

    setPlaybackState(prev => ({
      mode: 'live',
      isTransitioning: prev.isTransitioning,
      // Clear historical data
      currentRecording: undefined,
      selectedTransmission: undefined
    }))
  }, [setTransitioning, historicalTimeControl.controls])

  /**
   * Switch to historical playback mode
   */
  const switchToHistorical = useCallback((transmission: TimelineTransmission) => {
    console.log('Switching to historical mode:', transmission)

    // Set initial time in the historical time control system
    const startTime = new Date(transmission.start_time)
    historicalTimeControl.controls.setInitialTime(startTime)

    setTransitioning(true)

    setPlaybackState(prev => ({
      mode: 'historical',
      isTransitioning: prev.isTransitioning,
      currentRecording: transmission.recording,
      selectedTransmission: transmission
    }))
  }, [setTransitioning, historicalTimeControl.controls])

  /**
   * Clear current selection but stay in current mode
   */
  const clearSelection = useCallback(() => {
    setPlaybackState(prev => ({
      ...prev,
      currentRecording: undefined,
      selectedTransmission: undefined
    }))
  }, [])

  // Computed properties
  const isLive = playbackState.mode === 'live'
  const isHistorical = playbackState.mode === 'historical'
  const isTransitioning = playbackState.isTransitioning

  return {
    playbackState,
    currentTime: isHistorical ? historicalTimeControl.currentTime : null,
    switchToLive,
    switchToHistorical,
    clearSelection,
    isLive,
    isHistorical,
    isTransitioning,
    historicalTimeControls: isHistorical ? historicalTimeControl.controls : null,
    isHistoricalPlaying: historicalTimeControl.isPlaying
  }
}
