/**
 * Timeline Data Management Hook
 * 
 * Manages loading and caching of historical timeline data
 */

import { useState, useCallback, useEffect, useRef } from 'react'
import { historicalAPI } from '@/lib/historical-api'
import type { TimelineDay, TimelineTransmission } from '@/types'

export interface UseTimelineDataReturn {
  // Data
  timelineDays: TimelineDay[]
  
  // Loading states
  isLoading: boolean
  error: string | null
  
  // Actions
  loadDateRange: (startDate: Date, endDate: Date) => Promise<void>
  loadPreviousDays: (count: number) => Promise<void>
  refreshDay: (date: string) => Promise<void>
  
  // Utilities
  getTransmissionsForDate: (date: string) => TimelineTransmission[]
  findTransmissionById: (id: string) => TimelineTransmission | undefined
}

export const useTimelineData = (stationId: string): UseTimelineDataReturn => {
  const [timelineDays, setTimelineDays] = useState<TimelineDay[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const loadedDatesRef = useRef<Set<string>>(new Set())
  const earliestLoadedDateRef = useRef<Date | null>(null)

  /**
   * Load a range of dates
   */
  const loadDateRange = useCallback(async (startDate: Date, endDate: Date) => {
    setIsLoading(true)
    setError(null)

    try {
      console.log(`Loading timeline data from ${startDate.toISOString().slice(0, 10)} to ${endDate.toISOString().slice(0, 10)}`)
      
      const newDays = await historicalAPI.fetchDateRange(stationId, startDate, endDate)
      
      setTimelineDays(prevDays => {
        // Merge new days with existing ones, avoiding duplicates
        const existingDatesSet = new Set(prevDays.map(day => day.date))
        const uniqueNewDays = newDays.filter(day => !existingDatesSet.has(day.date))
        
        // Add to loaded dates tracking
        uniqueNewDays.forEach(day => {
          loadedDatesRef.current.add(day.date)
        })
        
        // Update earliest loaded date
        if (uniqueNewDays.length > 0) {
          const earliestNewDate = new Date(Math.min(...uniqueNewDays.map(day => new Date(day.date).getTime())))
          if (!earliestLoadedDateRef.current || earliestNewDate < earliestLoadedDateRef.current) {
            earliestLoadedDateRef.current = earliestNewDate
          }
        }
        
        // Combine and sort by date (newest first)
        const combined = [...prevDays, ...uniqueNewDays]
        return combined.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      })

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load timeline data'
      setError(errorMessage)
      console.error('Timeline data loading error:', err)
    } finally {
      setIsLoading(false)
    }
  }, [stationId])

  /**
   * Load previous days (for infinite scroll)
   */
  const loadPreviousDays = useCallback(async (count: number = 3) => {
    if (isLoading) return

    const startDate = earliestLoadedDateRef.current 
      ? new Date(earliestLoadedDateRef.current.getTime() - (count * 24 * 60 * 60 * 1000))
      : new Date(Date.now() - (count * 24 * 60 * 60 * 1000))
    
    const endDate = earliestLoadedDateRef.current 
      ? new Date(earliestLoadedDateRef.current.getTime() - (24 * 60 * 60 * 1000))
      : new Date(Date.now() - (24 * 60 * 60 * 1000))

    await loadDateRange(startDate, endDate)
  }, [loadDateRange, isLoading])

  /**
   * Refresh a specific day's data
   */
  const refreshDay = useCallback(async (date: string) => {
    const targetDate = new Date(date)
    
    // Clear from cache first
    historicalAPI.clearCache()
    
    try {
      const dailyRecordings = await historicalAPI.fetchDailyRecordings(stationId, targetDate)
      
      if (dailyRecordings) {
        const updatedDay = historicalAPI.convertToTimelineDay(dailyRecordings)
        
        setTimelineDays(prevDays => 
          prevDays.map(day => 
            day.date === date ? updatedDay : day
          )
        )
      }
    } catch (err) {
      console.error(`Failed to refresh day ${date}:`, err)
    }
  }, [stationId])

  /**
   * Get transmissions for a specific date
   */
  const getTransmissionsForDate = useCallback((date: string): TimelineTransmission[] => {
    const day = timelineDays.find(d => d.date === date)
    return day?.transmissions || []
  }, [timelineDays])

  /**
   * Find a transmission by ID
   */
  const findTransmissionById = useCallback((id: string): TimelineTransmission | undefined => {
    for (const day of timelineDays) {
      const transmission = day.transmissions.find(t => t.id === id)
      if (transmission) return transmission
    }
    return undefined
  }, [timelineDays])

  /**
   * Initialize with recent days on mount
   */
  useEffect(() => {
    const initializeTimeline = async () => {
      const today = new Date()
      const threeDaysAgo = new Date(today.getTime() - (3 * 24 * 60 * 60 * 1000))

      await loadDateRange(threeDaysAgo, today)
    }

    initializeTimeline()
  }, [loadDateRange])

  return {
    timelineDays,
    isLoading,
    error,
    loadDateRange,
    loadPreviousDays,
    refreshDay,
    getTransmissionsForDate,
    findTransmissionById
  }
}
