/**
 * Simple API test utility for development
 */

import { historicalAPI } from './historical-api'
import { config, getRecordingsStationId } from './config'

export async function testHistoricalAPI() {
  console.log('🧪 Testing Historical API...')
  
  try {
    const stationId = config.player.defaultStationId
    const fullStationId = getRecordingsStationId()
    const testDate = new Date()
    testDate.setDate(testDate.getDate() - 1) // Yesterday

    console.log(`📡 Testing with station: ${stationId} (${fullStationId})`)
    console.log(`📅 Testing date: ${testDate.toISOString().slice(0, 10)}`)

    // Test single day fetch
    const dailyRecordings = await historicalAPI.fetchDailyRecordings(stationId, testDate)
    
    if (dailyRecordings) {
      console.log('✅ API Response received:')
      console.log(`   - Station: ${dailyRecordings.station}`)
      console.log(`   - Date: ${dailyRecordings.date}`)
      console.log(`   - Total recordings: ${dailyRecordings.total_recordings}`)
      console.log(`   - Total duration: ${Math.floor(dailyRecordings.total_duration_ms / 1000)}s`)
      
      if (dailyRecordings.recordings.length > 0) {
        const firstRecording = dailyRecordings.recordings[0]
        console.log('   - First recording:')
        console.log(`     * Time: ${firstRecording.start_time}`)
        console.log(`     * Duration: ${Math.floor(firstRecording.duration_ms / 1000)}s`)
        console.log(`     * URL: ${firstRecording.download_url}`)
      }
    } else {
      console.log('⚠️  No recordings found for this date')
    }
    
    // Test cache
    console.log('\n🗄️  Testing cache...')
    const cacheStats = historicalAPI.getCacheStats()
    console.log(`   - Cache size: ${cacheStats.size}`)
    console.log(`   - Cache keys: ${cacheStats.keys.join(', ')}`)
    
    return dailyRecordings
    
  } catch (error) {
    console.error('❌ API Test failed:', error)
    return null
  }
}

// Auto-run test in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Run test after a short delay to avoid blocking initial render
  setTimeout(() => {
    testHistoricalAPI()
  }, 2000)
}
