/**
 * Historical Recordings API Service
 * 
 * Service layer for fetching historical recording data from the weather API
 */

import { config, getRecordingsStationId } from './config'
import type { DailyRecordings, HistoricalRecording, TimelineDay, TimelineTransmission } from '@/types'

export class HistoricalRecordingsAPI {
  private baseUrl: string
  private cache: Map<string, DailyRecordings> = new Map()
  private loadingPromises: Map<string, Promise<DailyRecordings | null>> = new Map()
  private cacheTimestamps: Map<string, number> = new Map()
  private readonly CACHE_TTL = 5 * 60 * 1000 // 5 minutes
  private readonly MAX_CACHE_SIZE = 100 // Maximum number of cached days

  constructor(baseUrl: string = config.api.baseUrl) {
    this.baseUrl = baseUrl.replace(/\/$/, '') // Remove trailing slash
  }

  /**
   * Format date for API calls (YYYYMMDD)
   */
  private formatDateForAPI(date: Date): string {
    return date.toISOString().slice(0, 10).replace(/-/g, '')
  }

  /**
   * Format date for cache keys (YYYY-MM-DD)
   */
  private formatDateForCache(date: Date): string {
    return date.toISOString().slice(0, 10)
  }

  /**
   * Generate cache key for a station and date
   */
  private getCacheKey(stationId: string, date: Date): string {
    return `${stationId}_${this.formatDateForCache(date)}`
  }

  /**
   * Check if cache entry is still valid
   */
  private isCacheValid(cacheKey: string): boolean {
    const timestamp = this.cacheTimestamps.get(cacheKey)
    if (!timestamp) return false
    return Date.now() - timestamp < this.CACHE_TTL
  }

  /**
   * Evict old cache entries to maintain size limit
   */
  private evictOldCacheEntries(): void {
    if (this.cache.size <= this.MAX_CACHE_SIZE) return

    // Sort by timestamp and remove oldest entries
    const entries = Array.from(this.cacheTimestamps.entries())
      .sort(([, a], [, b]) => a - b)

    const toRemove = entries.slice(0, entries.length - this.MAX_CACHE_SIZE)

    for (const [key] of toRemove) {
      this.cache.delete(key)
      this.cacheTimestamps.delete(key)
    }
  }

  /**
   * Fetch daily recordings for a specific station and date
   */
  async fetchDailyRecordings(stationId: string, date: Date): Promise<DailyRecordings | null> {
    // Use hardcoded station ID for recordings API
    const fullStationId = getRecordingsStationId()
    const cacheKey = this.getCacheKey(fullStationId, date)
    
    // Return cached data if available and valid
    if (this.cache.has(cacheKey) && this.isCacheValid(cacheKey)) {
      return this.cache.get(cacheKey)!
    }

    // Return existing promise if already loading
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey)!
    }

    // Create new loading promise
    const loadingPromise = this.performFetch(fullStationId, date, cacheKey)
    this.loadingPromises.set(cacheKey, loadingPromise)

    try {
      const result = await loadingPromise
      return result
    } finally {
      this.loadingPromises.delete(cacheKey)
    }
  }

  /**
   * Perform the actual API fetch
   */
  private async performFetch(stationId: string, date: Date, cacheKey: string): Promise<DailyRecordings | null> {
    try {
      const apiDate = this.formatDateForAPI(date)
      const url = `${this.baseUrl}/recordings/${stationId}/${apiDate}`
      
      console.log(`Fetching recordings for ${stationId} on ${apiDate}:`, url)

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(config.api.timeout)
      })

      if (!response.ok) {
        if (response.status === 404) {
          // No recordings for this date - return empty result
          const emptyResult: DailyRecordings = {
            station: stationId,
            date: apiDate,
            total_recordings: 0,
            total_duration_ms: 0,
            total_size_bytes: 0,
            recordings: []
          }
          this.evictOldCacheEntries()
          this.cache.set(cacheKey, emptyResult)
          this.cacheTimestamps.set(cacheKey, Date.now())
          return emptyResult
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data: DailyRecordings = await response.json()
      
      // Cache the result with timestamp
      this.evictOldCacheEntries()
      this.cache.set(cacheKey, data)
      this.cacheTimestamps.set(cacheKey, Date.now())

      console.log(`Fetched ${data.total_recordings} recordings for ${stationId} on ${apiDate}`)
      return data

    } catch (error) {
      console.error(`Failed to fetch recordings for ${stationId} on ${this.formatDateForAPI(date)}:`, error)
      return null
    }
  }

  /**
   * Convert DailyRecordings to TimelineDay format
   */
  convertToTimelineDay(dailyRecordings: DailyRecordings): TimelineDay {
    const transmissions: TimelineTransmission[] = dailyRecordings.recordings.map((recording, index) => {
      const startTime = new Date(recording.start_time)
      const endTime = new Date(startTime.getTime() + recording.duration_ms)

      return {
        id: `${dailyRecordings.station}_${dailyRecordings.date}_${index}`,
        start_time: startTime,
        end_time: endTime,
        duration_ms: recording.duration_ms,
        recording
      }
    })

    // Sort transmissions by start time (most recent first)
    transmissions.sort((a, b) => b.start_time.getTime() - a.start_time.getTime())

    return {
      date: this.formatDateForCache(new Date(dailyRecordings.date.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3'))),
      transmissions,
      isLoaded: true,
      isLoading: false
    }
  }

  /**
   * Fetch multiple days of recordings
   */
  async fetchDateRange(stationId: string, startDate: Date, endDate: Date): Promise<TimelineDay[]> {
    // Use hardcoded station ID for recordings API
    const fullStationId = getRecordingsStationId()
    const promises: Promise<TimelineDay>[] = []
    const currentDate = new Date(startDate)

    while (currentDate <= endDate) {
      const datePromise = this.fetchDailyRecordings(stationId, new Date(currentDate))
        .then(dailyRecordings => {
          if (dailyRecordings) {
            return this.convertToTimelineDay(dailyRecordings)
          } else {
            // Return empty day if fetch failed
            return {
              date: this.formatDateForCache(new Date(currentDate)),
              transmissions: [],
              isLoaded: false,
              isLoading: false
            }
          }
        })
      
      promises.push(datePromise)
      currentDate.setDate(currentDate.getDate() + 1)
    }

    return Promise.all(promises)
  }

  /**
   * Get recording stream URL for direct playback
   */
  getRecordingStreamUrl(recording: HistoricalRecording): string {
    return recording.download_url
  }

  /**
   * Clear cache (useful for testing or memory management)
   */
  clearCache(): void {
    this.cache.clear()
    this.cacheTimestamps.clear()
    this.loadingPromises.clear()
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number, keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
}

// Export singleton instance
export const historicalAPI = new HistoricalRecordingsAPI()
