'use client'

import React, { useCallback, useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Radio,
  History,
  ArrowLeft,
  AlertCircle,
  Wifi,
  WifiOff
} from 'lucide-react'
import { HLSPlayer } from './hls-player'
import { TimeBasedHistoricalPlayer } from './time-based-historical-player'
import { Timeline } from './timeline'
import { TimelineErrorBoundary } from './timeline-error-boundary'
import { PlayerSkeleton } from './player-skeleton'
import { usePlaybackMode } from '@/hooks/use-playback-mode'
import { toast } from 'sonner'
import type { TimelineTransmission } from '@/types'

// Import test utility in development
if (process.env.NODE_ENV === 'development') {
  import('@/lib/test-api')
}

interface UnifiedPlayerProps {
  stationId: string
  apiBaseUrl: string
  autoPlay?: boolean
  className?: string
}

export function UnifiedPlayer({
  stationId,
  apiBaseUrl,
  autoPlay = false,
  className = ''
}: UnifiedPlayerProps) {
  const {
    playbackState,
    currentTime,
    switchToLive,
    switchToHistorical,
    isLive,
    isHistorical,
    isTransitioning,
    historicalTimeControls,
    isHistoricalPlaying
  } = usePlaybackMode()

  const [isInitialLoading, setIsInitialLoading] = useState(true)
  const lastSelectionTimeRef = useRef<number>(0)

  // Simulate initial loading delay for better UX
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitialLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Only handle shortcuts when not typing in an input
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return
      }

      switch (event.key.toLowerCase()) {
        case 'l':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault()
            handleReturnToLive()
          }
          break
        case 'escape':
          if (isHistorical) {
            event.preventDefault()
            handleReturnToLive()
          }
          break
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [isHistorical])

  /**
   * Handle transmission selection from timeline
   */
  const handleTransmissionSelect = useCallback((transmission: TimelineTransmission) => {
    // Debounce rapid selections to prevent audio interruption errors
    const now = Date.now()
    if (now - lastSelectionTimeRef.current < 500) {
      return // Ignore rapid clicks
    }
    lastSelectionTimeRef.current = now

    console.log('Selected transmission:', transmission)
    const startTime = new Date(transmission.start_time).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
    toast.success(`Playing recording from ${startTime}`, {
      description: `Duration: ${Math.floor(transmission.duration_ms / 1000)}s`,
      duration: 3000
    })
    switchToHistorical(transmission)
  }, [switchToHistorical])

  /**
   * Handle return to live mode
   */
  const handleReturnToLive = useCallback(() => {
    toast.info('Switched to live mode', {
      description: 'Now playing real-time AWOS stream',
      duration: 2000
    })
    switchToLive()
  }, [switchToLive])

  /**
   * Handle historical playback end
   */
  const handleHistoricalPlaybackEnd = useCallback(() => {
    console.log('Historical playback ended')
    // Could auto-return to live or stay in historical mode
  }, [])

  /**
   * Handle playback errors
   */
  const handlePlaybackError = useCallback((error: string) => {
    // Filter out interruption errors which are expected during rapid switching
    if (error.includes('interrupted by a new load request')) {
      return // Don't show these errors to the user
    }

    console.error('Playback error:', error)
    toast.error('Playback Error', {
      description: error,
      duration: 5000
    })
  }, [])

  // Show loading skeleton during initial load
  if (isInitialLoading) {
    return <PlayerSkeleton />
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Main Player Card */}
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {isLive ? (
                <Radio className="w-5 h-5 text-green-600" />
              ) : (
                <History className="w-5 h-5 text-blue-600" />
              )}
              <CardTitle>
                {isLive ? 'Live AWOS Stream' : 'Historical Recording'}
              </CardTitle>
              {isTransitioning && (
                <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
              )}
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant={isLive ? 'default' : 'secondary'}>
                {isLive ? (
                  <div className="flex items-center gap-1">
                    <Wifi className="w-3 h-3" />
                    LIVE
                  </div>
                ) : (
                  <div className="flex items-center gap-1">
                    <WifiOff className="w-3 h-3" />
                    HISTORICAL
                  </div>
                )}
              </Badge>
              
              {isHistorical && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleReturnToLive}
                  className="gap-1"
                >
                  <ArrowLeft className="w-3 h-3" />
                  Back to Live
                </Button>
              )}
            </div>
          </div>
          
          <CardDescription>
            {isLive
              ? `Live audio streaming from Ridge Landing Airpark (VPS) - Station ${stationId}`
              : currentTime
                ? `Historical playback at ${currentTime.toLocaleString()}`
                : 'Historical audio playback'
            }
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Transition Alert */}
          {isTransitioning && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Switching playback mode...
              </AlertDescription>
            </Alert>
          )}

          {/* Player Content */}
          {isLive ? (
            <HLSPlayer
              stationId={stationId}
              apiBaseUrl={apiBaseUrl}
              autoPlay={autoPlay}
            />
          ) : (
            <TimeBasedHistoricalPlayer
              stationId={stationId}
              currentTime={currentTime}
              timeControls={historicalTimeControls}
              isPlaying={isHistoricalPlaying}
              onPlaybackEnd={handleHistoricalPlaybackEnd}
              onError={handlePlaybackError}
            />
          )}

          {/* This fallback is now handled inside TimeBasedHistoricalPlayer */}
        </CardContent>
      </Card>

      {/* Timeline */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
        {/* Timeline takes up 1 column on large screens */}
        <div className="lg:col-span-1">
          <TimelineErrorBoundary>
            <Timeline
              stationId={stationId}
              apiBaseUrl={apiBaseUrl}
              onTransmissionSelect={handleTransmissionSelect}
              selectedTransmission={playbackState.selectedTransmission}
            />
          </TimelineErrorBoundary>
        </div>

        {/* Additional info or controls could go in remaining columns */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">How to Use</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <strong>Live Mode:</strong> Listen to real-time AWOS transmissions as they happen
                </div>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <strong>Historical Mode:</strong> Click on any transmission to start time-based playback from that moment
                </div>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <strong>Timeline:</strong> Shows last 3 days. Scroll down to load older dates. Recordings are ordered newest to oldest
                </div>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <strong>Navigation:</strong> Use "Back to Live" button or press <kbd className="px-1 py-0.5 text-xs bg-muted rounded">Esc</kbd> to return to real-time streaming
                </div>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <strong>Time-Based Playback:</strong> Historical time continuously advances, automatically playing recordings when their time is reached
                </div>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-indigo-500 rounded-full mt-2 flex-shrink-0" />
                <div>
                  <strong>Shortcuts:</strong> Press <kbd className="px-1 py-0.5 text-xs bg-muted rounded">Ctrl+L</kbd> (or <kbd className="px-1 py-0.5 text-xs bg-muted rounded">⌘+L</kbd> on Mac) to quickly return to live mode
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
