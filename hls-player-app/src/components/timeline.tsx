'use client'

import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Skeleton } from '@/components/ui/skeleton'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Clock, 
  Calendar, 
  Radio, 
  ChevronUp,
  Loader2,
  AlertCircle
} from 'lucide-react'
import { useTimelineData } from '@/hooks/use-timeline-data'
import type { TimelineProps, TimelineTransmission } from '@/types'

/**
 * Memoized transmission item component for performance
 */
const TransmissionItem = React.memo(({
  transmission,
  isSelected,
  onSelect,
  formatTime,
  getDurationDisplay
}: {
  transmission: TimelineTransmission
  isSelected: boolean
  onSelect: (transmission: TimelineTransmission) => void
  formatTime: (date: Date) => string
  getDurationDisplay: (durationMs: number) => string
}) => (
  <button
    onClick={() => onSelect(transmission)}
    className={`w-full text-left p-2 rounded-md border transition-colors hover:bg-muted/50 ${
      isSelected
        ? 'bg-primary/10 border-primary'
        : 'border-border'
    }`}
  >
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-2">
        <div className="w-2 h-2 bg-blue-500 rounded-full" />
        <span className="text-sm font-medium">
          {formatTime(transmission.start_time)}
        </span>
      </div>
      <Badge variant="outline" className="text-xs">
        {getDurationDisplay(transmission.duration_ms)}
      </Badge>
    </div>
  </button>
))

export function Timeline({ 
  stationId, 
  onTransmissionSelect, 
  selectedTransmission,
  className = '' 
}: TimelineProps) {
  const {
    timelineDays,
    isLoading,
    error,
    loadPreviousDays
  } = useTimelineData(stationId)

  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [showScrollToTop, setShowScrollToTop] = useState(false)

  /**
   * Format time for display
   */
  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    })
  }

  /**
   * Format date for display
   */
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString)
    const today = new Date()
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
    
    if (date.toDateString() === today.toDateString()) {
      return 'Today'
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday'
    } else {
      return date.toLocaleDateString('en-US', { 
        weekday: 'short', 
        month: 'short', 
        day: 'numeric' 
      })
    }
  }

  /**
   * Handle transmission click
   */
  const handleTransmissionClick = useCallback((transmission: TimelineTransmission) => {
    onTransmissionSelect(transmission)
  }, [onTransmissionSelect])

  /**
   * Memoized timeline days to prevent unnecessary re-renders
   */
  const memoizedTimelineDays = useMemo(() => {
    // Only show last 10 days to prevent performance issues (since we're only fetching 3 days at a time)
    return timelineDays.slice(0, 10)
  }, [timelineDays])

  /**
   * Handle scroll to load more data
   */
  const handleScroll = useCallback(async (event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = event.currentTarget
    
    // Show/hide scroll to top button
    setShowScrollToTop(scrollTop > 200)
    
    // Load more when near bottom
    const threshold = 100
    if (scrollHeight - scrollTop - clientHeight < threshold && !isLoading && !isLoadingMore) {
      setIsLoadingMore(true)
      try {
        await loadPreviousDays(3)
      } finally {
        setIsLoadingMore(false)
      }
    }
  }, [isLoading, isLoadingMore, loadPreviousDays])

  /**
   * Scroll to top
   */
  const scrollToTop = useCallback(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTo({ top: 0, behavior: 'smooth' })
    }
  }, [])

  /**
   * Get duration display
   */
  const getDurationDisplay = (durationMs: number): string => {
    const seconds = Math.floor(durationMs / 1000)
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    
    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`
    }
    return `${remainingSeconds}s`
  }

  if (error) {
    return (
      <Card className={`w-full ${className}`}>
        <CardContent className="p-6 text-center">
          <AlertCircle className="w-8 h-8 text-red-500 mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">Failed to load timeline</p>
          <p className="text-xs text-red-500 mt-1">{error}</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={`w-full ${className}`}>
      <CardContent className="p-0">
        {/* Header */}
        <div className="p-4 border-b bg-muted/50">
          <div className="flex items-center gap-2">
            <Clock className="w-4 h-4" />
            <h3 className="font-semibold">Timeline</h3>
            <Badge variant="outline" className="ml-auto">
              {stationId}
            </Badge>
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Click on transmissions to play historical audio
          </p>
        </div>

        {/* Timeline Content */}
        <div className="relative">
          <ScrollArea 
            ref={scrollAreaRef}
            className="h-[400px]" 
            onScrollCapture={handleScroll}
          >
            <div className="p-4 space-y-4">
              {/* Live indicator at top */}
              <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                <span className="text-sm font-medium text-green-700 dark:text-green-300">
                  LIVE NOW
                </span>
                <Radio className="w-4 h-4 text-green-600 dark:text-green-400 ml-auto" />
              </div>

              {/* Timeline days */}
              {memoizedTimelineDays.map((day) => (
                <div key={day.date} className="space-y-2">
                  {/* Date header */}
                  <div className="flex items-center gap-2 py-2">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <h4 className="font-medium text-sm">
                      {formatDate(day.date)}
                    </h4>
                    <div className="flex-1 h-px bg-border" />
                    <Badge variant="secondary" className="text-xs">
                      {day.transmissions.length} transmissions
                    </Badge>
                  </div>

                  {/* Transmissions */}
                  {day.transmissions.length > 0 ? (
                    <div className="space-y-1 ml-6">
                      {day.transmissions.map((transmission) => (
                        <TransmissionItem
                          key={transmission.id}
                          transmission={transmission}
                          isSelected={selectedTransmission?.id === transmission.id}
                          onSelect={handleTransmissionClick}
                          formatTime={formatTime}
                          getDurationDisplay={getDurationDisplay}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="ml-6 p-2 text-center text-muted-foreground text-sm">
                      No transmissions recorded
                    </div>
                  )}
                </div>
              ))}

              {/* Loading more indicator */}
              {isLoadingMore && (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  <span className="text-sm text-muted-foreground">Loading more...</span>
                </div>
              )}

              {/* Initial loading skeleton */}
              {isLoading && timelineDays.length === 0 && (
                <div className="space-y-4">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="space-y-2">
                      <Skeleton className="h-6 w-32" />
                      <div className="ml-6 space-y-1">
                        <Skeleton className="h-8 w-full" />
                        <Skeleton className="h-8 w-full" />
                        <Skeleton className="h-8 w-3/4" />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </ScrollArea>

          {/* Scroll to top button */}
          {showScrollToTop && (
            <Button
              size="sm"
              variant="outline"
              className="absolute bottom-4 right-4 shadow-lg"
              onClick={scrollToTop}
            >
              <ChevronUp className="w-4 h-4" />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
