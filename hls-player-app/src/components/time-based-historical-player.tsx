'use client'

import React, { useRef, useState, useEffect, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  Square,
  AlertCircle,
  Clock,
  SkipForward,
  SkipBack
} from 'lucide-react'
import { useTimelineData } from '@/hooks/use-timeline-data'
import type { TimelineTransmission } from '@/types'
import type { HistoricalTimeControls } from '@/hooks/use-historical-time-control'

interface TimeBasedHistoricalPlayerProps {
  stationId: string
  currentTime: Date | null
  timeControls: HistoricalTimeControls | null
  isPlaying: boolean
  onPlaybackEnd?: () => void
  onError?: (error: string) => void
  className?: string
}

export function TimeBasedHistoricalPlayer({
  stationId,
  currentTime,
  timeControls,
  isPlaying,
  onPlaybackEnd,
  onError,
  className = ''
}: TimeBasedHistoricalPlayerProps) {
  const audioRef = useRef<HTMLAudioElement>(null)
  const { timelineDays } = useTimelineData(stationId)

  const [isMuted, setIsMuted] = useState(false)
  const [volume, setVolume] = useState(1)
  const [currentRecording, setCurrentRecording] = useState<TimelineTransmission | null>(null)
  const [error, setError] = useState<string | null>(null)

  /**
   * Find the recording that should be playing at the current time
   */
  const findCurrentRecording = useCallback((time: Date): TimelineTransmission | null => {
    for (const day of timelineDays) {
      for (const transmission of day.transmissions) {
        const startTime = new Date(transmission.start_time)
        const endTime = new Date(transmission.end_time)
        
        if (time >= startTime && time <= endTime) {
          return transmission
        }
      }
    }
    return null
  }, [timelineDays])

  /**
   * Calculate playback position within the current recording
   */
  const getPlaybackPosition = useCallback((time: Date, recording: TimelineTransmission): number => {
    const startTime = new Date(recording.start_time)
    const elapsed = time.getTime() - startTime.getTime()
    return Math.max(0, elapsed / 1000) // Convert to seconds
  }, [])

  /**
   * Handle audio playback
   */
  const playAudio = useCallback(async (recording: TimelineTransmission, position: number = 0) => {
    if (!audioRef.current) return

    try {
      const audio = audioRef.current
      
      // Only change source if it's a different recording
      if (audio.src !== recording.recording.download_url) {
        audio.src = recording.recording.download_url
        await new Promise((resolve, reject) => {
          const handleLoad = () => {
            audio.removeEventListener('loadeddata', handleLoad)
            audio.removeEventListener('error', handleError)
            resolve(void 0)
          }
          const handleError = () => {
            audio.removeEventListener('loadeddata', handleLoad)
            audio.removeEventListener('error', handleError)
            reject(new Error('Failed to load audio'))
          }
          audio.addEventListener('loadeddata', handleLoad)
          audio.addEventListener('error', handleError)
        })
      }

      // Set playback position
      audio.currentTime = position

      // Only start playing if the time control system is in playing state
      if (isPlaying) {
        await audio.play()
      } else {
        // Ensure audio is paused if time control is paused
        audio.pause()
      }

      setError(null)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Playback failed'
      setError(errorMessage)
      onError?.(errorMessage)
    }
  }, [isPlaying, onError])

  /**
   * Stop audio playback
   */
  const stopAudio = useCallback(() => {
    if (!audioRef.current) return

    audioRef.current.pause()
  }, [])

  /**
   * Handle volume change
   */
  const handleVolumeChange = useCallback((newVolume: number) => {
    if (!audioRef.current) return
    
    const clampedVolume = Math.max(0, Math.min(1, newVolume))
    audioRef.current.volume = clampedVolume
    setVolume(clampedVolume)
    setIsMuted(clampedVolume === 0)
  }, [])

  /**
   * Toggle mute
   */
  const toggleMute = useCallback(() => {
    if (!audioRef.current) return
    
    if (isMuted) {
      audioRef.current.volume = volume
      setIsMuted(false)
    } else {
      audioRef.current.volume = 0
      setIsMuted(true)
    }
  }, [isMuted, volume])

  /**
   * Handle time control actions
   */
  const handlePlayPause = useCallback(() => {
    timeControls?.togglePlayPause()
  }, [timeControls])

  const handleJumpForward = useCallback(() => {
    timeControls?.jumpForward(15)
  }, [timeControls])

  const handleJumpBackward = useCallback(() => {
    timeControls?.jumpBackward(15)
  }, [timeControls])

  /**
   * Stop playback completely
   */
  const handleStop = useCallback(() => {
    timeControls?.pause()
    stopAudio()
    setCurrentRecording(null)
    onPlaybackEnd?.()
  }, [stopAudio, onPlaybackEnd, timeControls])

  /**
   * Main effect: Handle time-based playback
   */
  useEffect(() => {
    if (!currentTime) {
      stopAudio()
      setCurrentRecording(null)
      return
    }

    const targetRecording = findCurrentRecording(currentTime)
    
    if (targetRecording) {
      if (!currentRecording || currentRecording.id !== targetRecording.id) {
        // New recording should start playing
        setCurrentRecording(targetRecording)
        const position = getPlaybackPosition(currentTime, targetRecording)
        playAudio(targetRecording, position)
      } else {
        // Same recording, sync position if needed
        if (audioRef.current && isPlaying) {
          const expectedPosition = getPlaybackPosition(currentTime, targetRecording)
          const actualPosition = audioRef.current.currentTime
          const drift = Math.abs(expectedPosition - actualPosition)
          
          // Sync if drift is more than 2 seconds
          if (drift > 2) {
            audioRef.current.currentTime = expectedPosition
          }
        }
      }
    } else {
      // No recording should be playing at this time
      if (currentRecording) {
        stopAudio()
        setCurrentRecording(null)
      }
    }
  }, [currentTime, currentRecording, findCurrentRecording, getPlaybackPosition, playAudio, stopAudio, isPlaying])

  /**
   * Handle play/pause state changes for current audio
   */
  useEffect(() => {
    if (!audioRef.current || !currentRecording) return

    if (isPlaying) {
      // Resume playback if paused
      if (audioRef.current.paused) {
        audioRef.current.play().catch(err => {
          console.error('Failed to resume audio:', err)
          setError('Failed to resume playback')
        })
      }
    } else {
      // Pause playback
      if (!audioRef.current.paused) {
        audioRef.current.pause()
      }
    }
  }, [isPlaying, currentRecording])

  /**
   * Format time for display
   */
  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    })
  }

  if (!currentTime) {
    return (
      <div className={`text-center py-8 text-muted-foreground ${className}`}>
        <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
        <p>Select a transmission from the timeline to start time-based playback</p>
      </div>
    )
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Hidden audio element */}
      <audio ref={audioRef} className="hidden" />

      {/* Current time display */}
      <div className="text-center">
        <div className="text-2xl font-mono font-bold">
          {formatTime(currentTime)}
        </div>
        <div className="text-sm text-muted-foreground">
          Historical Playback Time
        </div>
      </div>

      {/* Current recording info */}
      {currentRecording ? (
        <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
            <span className="text-sm font-medium">
              Playing: {formatTime(new Date(currentRecording.start_time))}
            </span>
          </div>
          <Badge variant="outline" className="text-xs">
            {Math.floor(currentRecording.duration_ms / 1000)}s
          </Badge>
        </div>
      ) : (
        <div className="flex items-center justify-center p-3 bg-muted/20 rounded-lg">
          <span className="text-sm text-muted-foreground">
            No recording at this time - silence
          </span>
        </div>
      )}

      {/* Error display */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Controls */}
      <div className="flex items-center justify-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleStop}
        >
          <Square className="w-4 h-4" />
        </Button>

        {/* Jump backward 15s */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleJumpBackward}
          disabled={!timeControls}
          title="Jump back 15 seconds"
        >
          <SkipBack className="w-4 h-4" />
          <span className="ml-1 text-xs">15s</span>
        </Button>

        {/* Play/Pause */}
        <Button
          onClick={handlePlayPause}
          disabled={!timeControls}
          size="lg"
          className="w-12 h-12 rounded-full"
        >
          {isPlaying ? (
            <Pause className="w-5 h-5" />
          ) : (
            <Play className="w-5 h-5" />
          )}
        </Button>

        {/* Jump forward 15s */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleJumpForward}
          disabled={!timeControls}
          title="Jump forward 15 seconds"
        >
          <span className="mr-1 text-xs">15s</span>
          <SkipForward className="w-4 h-4" />
        </Button>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={toggleMute}
          >
            {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
          </Button>

          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={isMuted ? 0 : volume}
            onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
            className="w-16 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
          />
        </div>
      </div>
    </div>
  )
}
